#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra logic lựa chọn cấu hình <PERSON>Guy<PERSON>
"""

import sys
import os

# Thêm path để import được các module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_behavior_vietguys_api_config():
    """Test BehaviorVietGuysApi với các config khác nhau"""
    try:
        from app.libs.apis.third_party_api.behavior_vietguys_api import BehaviorVietGuysApi
        
        print("=== Test BehaviorVietGuysApi Config Selection ===")
        
        # Test default config
        print("\n1. Test default config:")
        api_default = BehaviorVietGuysApi(config_type='default')
        print(f"   Title: {api_default.title}")
        print(f"   Config type: {api_default.config_type}")
        
        # Test urbox config
        print("\n2. Test urbox config:")
        api_urbox = BehaviorVietGuysApi(config_type='urbox')
        print(f"   Title: {api_urbox.title}")
        print(f"   Config type: {api_urbox.config_type}")
        
        # Test jomo config
        print("\n3. Test jomo config:")
        api_jomo = BehaviorVietGuysApi(config_type='jomo')
        print(f"   Title: {api_jomo.title}")
        print(f"   Config type: {api_jomo.config_type}")
        
        print("\n✅ BehaviorVietGuysApi config test passed!")
        return True
        
    except Exception as e:
        print(f"❌ BehaviorVietGuysApi config test failed: {e}")
        return False

def test_context_code_api_behavior_selection():
    """Test ContextCodeApi behavior selection logic"""
    try:
        from app.libs.apis.third_party_api.context_code_api import ContextCodeApi
        from app.const import DEFINE_SUPPLIER_VIETGUYS
        
        print("\n=== Test ContextCodeApi Behavior Selection ===")
        
        # Mock supplier data
        supplier = {
            "whoexport": 2,
            "supplier_api_config": {
                "class_api": DEFINE_SUPPLIER_VIETGUYS
            }
        }
        
        # Test với contract_signed_with = 'urbox'
        print("\n1. Test với contract_signed_with = 'urbox':")
        product_urbox = {"contract_signed_with": "urbox"}
        behavior_urbox = ContextCodeApi.get_behavior_by_supplier(supplier, product_urbox)
        if behavior_urbox:
            print(f"   Behavior title: {behavior_urbox.title}")
            print(f"   Config type: {behavior_urbox.config_type}")
        
        # Test với contract_signed_with = 'jomo'
        print("\n2. Test với contract_signed_with = 'jomo':")
        product_jomo = {"contract_signed_with": "jomo"}
        behavior_jomo = ContextCodeApi.get_behavior_by_supplier(supplier, product_jomo)
        if behavior_jomo:
            print(f"   Behavior title: {behavior_jomo.title}")
            print(f"   Config type: {behavior_jomo.config_type}")
        
        # Test với contract_signed_with = None
        print("\n3. Test với contract_signed_with = None:")
        product_default = {"contract_signed_with": None}
        behavior_default = ContextCodeApi.get_behavior_by_supplier(supplier, product_default)
        if behavior_default:
            print(f"   Behavior title: {behavior_default.title}")
            print(f"   Config type: {behavior_default.config_type}")
        
        # Test với product = None
        print("\n4. Test với product = None:")
        behavior_none = ContextCodeApi.get_behavior_by_supplier(supplier, None)
        if behavior_none:
            print(f"   Behavior title: {behavior_none.title}")
            print(f"   Config type: {behavior_none.config_type}")
        
        print("\n✅ ContextCodeApi behavior selection test passed!")
        return True
        
    except Exception as e:
        print(f"❌ ContextCodeApi behavior selection test failed: {e}")
        return False

def test_other_suppliers_not_affected():
    """Test rằng các supplier khác không bị ảnh hưởng"""
    try:
        from app.libs.apis.third_party_api.context_code_api import ContextCodeApi
        from app.const import DEFINE_SUPPLIER_VMG
        
        print("\n=== Test Other Suppliers Not Affected ===")
        
        # Mock supplier VMG
        supplier_vmg = {
            "whoexport": 2,
            "supplier_api_config": {
                "class_api": DEFINE_SUPPLIER_VMG
            }
        }
        
        product = {"contract_signed_with": "urbox"}
        behavior = ContextCodeApi.get_behavior_by_supplier(supplier_vmg, product)
        
        if behavior:
            print(f"   VMG Behavior title: {behavior.title}")
            print("   ✅ VMG supplier works normally")
        else:
            print("   ❌ VMG supplier behavior is None")
            
        print("\n✅ Other suppliers test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Other suppliers test failed: {e}")
        return False

def main():
    """Chạy tất cả tests"""
    print("🚀 Starting VietGuys Config Selection Tests...")
    
    tests = [
        test_behavior_vietguys_api_config,
        test_context_code_api_behavior_selection,
        test_other_suppliers_not_affected
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! VietGuys config selection is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
