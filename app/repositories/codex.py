#!/usr/bin/env python
# -*- coding: utf-8 -*-
from datetime import datetime, date

from loguru import logger

from app.const import CODE_AVAILABLE_DELTA_DAYS, LIMIT_CODES, STATUS_OFF, STATUS_ON
from app.helper import Helper

from app import models as m
from sqlalchemy import or_, asc, desc

from app.libs.base.utility import Utility
from app.security import UrboxTripleDes


class CodexRepositories(object):
    def __init__(self):
        self.model = m.Codex

    # TODO: refactor all code
    def update_code_to_storage(self, code):
        return (
            self.model.query()
            .filter(self.model.id == code["id"])
            .update({"is_redis": STATUS_OFF})
        )

    # TODO: Refactor all code
    def mark_code_as_to_redis(self, id):
        if not id:
            return False

        return (
            self.model.query()
            .filter(self.model.id == id)
            .update({"is_redis": STATUS_ON})
        )

    # TODO: Refactor all code
    # TODO: Chỉnh lại constants
    def get_unused_by_product_id(self, product_id):
        codes = []
        if not product_id:
            return codes

        return (
            self.model.query()
            .filter(
                self.model.product_id == product_id,
                self.model.is_export_codex == STATUS_ON,
                or_(
                    self.model.expired_time == 0,
                    self.model.expired_time
                    > Utility.add_days(date.today(), CODE_AVAILABLE_DELTA_DAYS),
                ),
            )
            .order_by(asc(self.model.expired_time))
            .all()
        )

    # TODO: refactor all code
    def get_by_codex(self, codes: list):
        codes_exist = []

        if len(codes) == 0:
            return codes_exist

        return self.model.query().filter(self.model.codex.in_(codes)).all()

    # TODO: refactor all code
    def get_by_codex_and_product_parent_id(self, codes: list, product_parent_id: int):
        codes_exist = []

        if len(codes) == 0:
            return codes_exist

        return (
            self.model.query()
            .filter(
                self.model.product_parent_id == product_parent_id,
                self.model.codex.in_(codes),
            )
            .all()
        )

    # TODO: refactor all code
    def get_by_codex_int_and_product_parent_id(self, list_codex_int: list, product_parent_id: int):
        list_codex_exist = []

        if len(list_codex_int) == 0:
            return list_codex_exist

        return (
            self.model.query()
            .filter(
                self.model.product_parent_id == product_parent_id,
                self.model.codex_int.in_(list_codex_int),
            )
            .all()
        )

    # TODO: refactor all code
    # TODO: refactor lại constants
    def get_code_outside_redis(self):

        return (
            self.model.query()
            .filter(
                self.model.status == STATUS_ON,
                self.model.is_redis == STATUS_OFF,
                self.model.transaction_id == None,
                or_(
                    self.model.expired_time == 0,
                    self.model.expired_time
                    > Utility.add_days(date.today(), CODE_AVAILABLE_DELTA_DAYS),
                ),
            )
            .order_by(desc(self.model.expired_time))
            .limit(LIMIT_CODES)
            .all()
        )

    def get_codex_not_in_redis(self):
        return (
            self.model.query()
            .filter(
                self.model.status == STATUS_ON,
                self.model.is_redis == STATUS_OFF,
                self.model.is_export_codex == STATUS_ON,
            )
            .order_by(asc(self.model.expired_time))
            .limit(LIMIT_CODES)
            .all()
        )

    def get_product_not_in_redis(self):
        codes = (
            self.model.query()
            .filter(
                self.model.status == STATUS_ON,
                self.model.is_redis == STATUS_OFF,
                self.model.is_export_codex == STATUS_ON,
                self.model.product_id != 0,
            )
            .group_by(self.model.product_id)
            .all()
        )
        return [code.product_id for code in codes]

    def get_codex_not_in_redis_by_product_id(self, product_id: int, limit: int):
        return (
            self.model.query()
            .filter(
                self.model.status == STATUS_ON,
                self.model.is_redis == STATUS_OFF,
                self.model.is_export_codex == STATUS_ON,
                self.model.product_id == product_id,
                or_(
                    self.model.expired_time == 0,
                    self.model.expired_time
                    > Utility.add_days(date.today(), CODE_AVAILABLE_DELTA_DAYS),
                ),
            )
            .order_by(asc(self.model.expired_time))
            .limit(limit)
            .all()
        )

    # TODO: refactor all file
    def create(self, *args, **kwargs):
        creator = self.model(
            supplier_id=kwargs.get("supplier_id") or None,
            product_id=kwargs.get("product_id") or None,
            serial=kwargs.get("serial") or None,
            pin=kwargs.get("pin") or None,
            codex=kwargs.get("codex") or None,
            codex_int=kwargs.get("codex_int") or Utility.string_folding(kwargs.get("codex") or None),
            process=kwargs.get("process") or 111,
            expired_time=kwargs.get("expired_time") or None,
            code_type=kwargs.get("code_type") or None,
            order_code=kwargs.get("order_code") or None,
            transaction_id=kwargs.get("transaction_id") or None,
            is_redis=kwargs.get("is_redis") or STATUS_OFF,
            is_export_codex=kwargs.get("is_export_codex") or STATUS_ON,
            is_giftcode=kwargs.get("is_giftcode") or STATUS_OFF,
            product_parent_id=kwargs.get("product_parent_id") or None,
            debt_recognition=kwargs.get("debt_recognition") or 1,
            created=Helper.get_now_unix_timestamp(),
            contract_code=kwargs.get("contract_code") or "",
        )
        creator.save()
        return creator

    def create_many(self, entities):
        data = []
        for entity in entities:
            data.append(self.model(entity))
        creator = self.model.create_many(m.Codex, entities)
        return creator

    # TODO: refactor all file
    def get_unused_code_by_ids(self, ids: list):
        if (len(ids)) == 0:
            return []

        return (
            self.model.query()
            .filter(self.model.id.in_(ids), self.model.is_export_codex == STATUS_ON)
            .all()
        )

    # TODO: refactor all code
    def update_expired_codex(self, codex, update_expired):
        return (
            self.model.query()
            .filter(self.model.codex == UrboxTripleDes.encode(codex))
            .update(
                {
                    "expired_time": update_expired,
                    "updated": Helper.get_now_unix_timestamp(),
                }
            )
        )

    # TODO: refactor all code
    def update_transaction_id(self, ids, transaction_id):
        if len(ids) > 0:
            self.model.query().filter(self.model.id.in_(ids)).update(
                {
                    "transaction_id": transaction_id,
                    "process": 210,
                    "is_export_codex": STATUS_OFF,
                },
                synchronize_session="fetch",
            )

    # TODO: refactor all code
    def check_not_exist_transaction_id(self, transaction_id):
        if transaction_id is None:
            return False

        valid = False
        try:
            query = (
                self.model.query()
                .filter(self.model.transaction_id == transaction_id)
                .all()
            )
            valid = True if len(query) == 0 else False
        except Exception as e:
            logger.exception(e)

        return valid


codex_repo = CodexRepositories()
