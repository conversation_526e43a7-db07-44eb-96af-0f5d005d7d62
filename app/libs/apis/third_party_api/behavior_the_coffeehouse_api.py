# coding: utf8

from loguru import logger

import requests

from app.const import (
    THIRD_PARTY_THE_COFFEE_HOUSE_AUTHOR_CODE,
    THIRD_PARTY_THE_COFFEE_HOUSE_SECRET_CODE,
    THIRD_PARTY_THE_COFFEE_HOUSE_PATH_GET_CODE,
    THIRD_PARTY_THE_COFFEE_HOUSE_HOST,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import BehaviorCodeApi


class BehaviorTheCoffeeHouseApi(AbstractApi, BehaviorCode<PERSON>pi):
    def __init__(self, *args, **kwargs):
        self.title = "TheCoffeeHouseApi"
        self.host = THIRD_PARTY_THE_COFFEE_HOUSE_HOST
        self.path_get_code = THIRD_PARTY_THE_COFFEE_HOUSE_PATH_GET_CODE
        self.secret_code = THIRD_PARTY_THE_COFFEE_HOUSE_SECRET_CODE
        self.author_code = THIRD_PARTY_THE_COFFEE_HOUSE_AUTHOR_CODE
        self.validate_api_config(
            Host=self.host,
            PathGetCode=self.path_get_code,
            SecretCode=self.secret_code,
            AuthorCode=self.author_code,
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        codes = []
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        try:
            headers = self.get_headers()
            body = self.get_body_path_get_code(product_code)
            url = "{host}{path}".format(host=self.host, path=self.path_get_code)
            for i in range(0, quantity):
                log = self.create_logs(
                    transaction_id=transaction_id,
                    merchant_name=self.title,
                    url=url,
                    headers=headers,
                    data=body,
                    contract_code=product_info.get("contract_code")
                )
                response = requests.post(url, json=body, headers=headers)
                if log is not None:
                    self.update_logs(
                        log=log,
                        headers=response.headers,
                        response_code=response.status_code,
                        data=response.json(),
                    )
                if response and response.status_code == 200:
                    content = response.json()
                    # content = {'barcode': 'TQ103NHHSUXGWB', 'expire_date': '2021-10-17T23:59:59.000Z'}
                    if "barcode" in content and content.get("barcode") != "":
                        expired = Utility.convert_datetime_to_unixtimestamp(
                            content.get("expire_date"), "%Y-%m-%dT%H:%M:%S.000Z"
                        )
                        code = self.format_code(
                            serial=None,
                            pin=None,
                            codex=content.get("barcode"),
                            expired=expired,
                        )
                        codes.append(code)
                else:
                    Utility.send_telegram_message_to_group(
                        message=f"TheCoffeeHouseApi get code failed: {response.status_code} - {response.text}"
                    )
            codes = self.unique_codex_by_product_parent_id(
                codes, product_info.get("product_parent_id")
            )
            # TODO: save log
        except Exception as e:
            logger.error(e)
            Utility.send_telegram_message_to_group(
                message=f"TheCoffeeHouseApi get code Exception: {str(e)}"
            )
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code,transaction_id)
        return codes, {"transaction_id": transaction_id}

    def get_headers(self):
        return {"Content-type": "application/json", "Authorization": self.author_code}

    def get_body_path_get_code(self, product_code):
        return dict(
            product_code=product_code,
            customer_name="",
            customer_phone="",
            secret_key=self.secret_code,
        )

    def get_limit_code(self):
        pass
