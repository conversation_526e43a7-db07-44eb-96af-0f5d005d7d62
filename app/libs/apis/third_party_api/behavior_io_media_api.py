# coding: utf8

from loguru import logger

import requests

from app.const import (
    THIRD_PARTY_IO_MEDIA_HOST,
    THIRD_PARTY_IO_MEDIA_PATH_CHECK_BALANCE,
    THIRD_PARTY_IO_MEDIA_USERNAME,
    THIRD_PARTY_IO_MEDIA_EMAIL_RECEIVE,
    THIRD_PARTY_IO_MEDIA_PATH_GET_CODE,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Behavior<PERSON>ode<PERSON><PERSON>
from app.security import UrboxOpenSsl


class BehaviorIoMediaApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "IoMediaApi"
        self.host = THIRD_PARTY_IO_MEDIA_HOST
        self.path_get_code = THIRD_PARTY_IO_MEDIA_PATH_GET_CODE
        self.path_check_balance = THIRD_PARTY_IO_MEDIA_PATH_CHECK_BALANCE
        self.username = THIRD_PARTY_IO_MEDIA_USERNAME
        self.email_receive = THIRD_PARTY_IO_MEDIA_EMAIL_RECEIVE
        self.validate_api_config(
            host=self.host,
            path_get_code=self.path_get_code,
            path_check_balance=self.path_check_balance,
            username=self.username,
            email_receive=self.email_receive,
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        if quantity > product_info.get('max_codes_per_call'):
            quantity = product_info.get('max_codes_per_call')
        codes = []
        # if self.__check_balance(0) is False:
        if self.__check_balance(price * quantity) is False:
            Utility.send_telegram_message_to_group(
                message=f"IoMediaApi get code: số dư không đủ "
            )
            return codes
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
        )
        body = self.__get_body_path_get_code(product_code, quantity)
        headers = self.__get_headers()
        url = "{host}{path}".format(host=self.host, path=self.path_get_code)

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
            contract_code=product_info.get("contract_code")
        )
        try:
            response = requests.post(url, json=body, headers=headers)
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data=response.json(),
                )

            self.after_get_code()
            if response and response.status_code == 200:
                response_body = response.json()
                raw_sign_data = "{resCode}{resMessage}{partnerCode}{partnerTransId}{totalValue}{discountValue}{debitValue}".format(
                    resCode=response_body.get("resCode") or '',
                    resMessage=response_body.get("resMessage") or '',
                    partnerCode=response_body.get("partnerCode") or '',
                    partnerTransId=response_body.get('partnerTransId') or '',
                    totalValue=response_body.get('totalValue') or '',
                    discountValue=response_body.get('discountValue') or '',
                    debitValue=response_body.get('debitValue') or ''
                )

                logger.info(f'raw_sign_data: {raw_sign_data}')
                if (
                    response_body.get("resCode") == "00"
                    and response_body.get("cardList")
                    and self.__verify_signature(
                        raw_sign_data, response_body.get("sign")
                    )
                ):
                    code_lists = response_body.get("cardList")
                    for code_item in code_lists:
                        codex = self.__decrypt_codex(code_item.get("pincode"))
                        code = self.format_code(
                            serial=code_item.get("serial"),
                            pin=None,
                            codex=codex,
                            expired=code_item.get("expiredate") / 1000,
                        )
                        codes.append(code)
                    codes = self.unique_codex_by_product_parent_id(
                        codes, product_info.get("product_parent_id")
                    )
                    # TODO: save log
            else:
                Utility.send_telegram_message_to_group(
                    message=f"IoMediaApi get code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"IoMediaApi get code Exception: {str(e)}"
            )
            self.after_get_code()
        quantity_success = len(codes)
        quantity_error = quantity - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(codes, order_id, product_id, price, product_code,transaction_id)
        return codes, {"transaction_id": transaction_id}

    def retrieve_card(self,transaction_id):
        codes = []
        raw_signature = "{partner_code}{transaction_id}".format(
            partner_code=self.username,
            transaction_id=transaction_id,
        )
        signature = self.__generate_signature(raw_signature)
        body = dict(
            partnerCode=self.username,
            partnerTransId=transaction_id,
            sign=signature,
        )
        headers = self.__get_headers()
        url = "{host}{path}".format(host=self.host, path='/IPayService/rest/partner/retrieveCardInfo')

        try:
            response = requests.post(url, json=body, headers=headers)
            if response and response.status_code == 200:
                response_body = response.json()
                raw_sign_data = "{resCode}{resMessage}{partnerCode}{partnerTransId}{totalValue}{discountValue}{debitValue}".format(
                    resCode=response_body.get("resCode") or '',
                    resMessage=response_body.get("resMessage") or '',
                    partnerCode=response_body.get("partnerCode") or '',
                    partnerTransId=response_body.get('partnerTransId') or '',
                    totalValue=response_body.get('totalValue') or '',
                    discountValue=response_body.get('discountValue') or '',
                    debitValue=response_body.get('debitValue') or ''
                )

                logger.info(f'raw_sign_data: {raw_sign_data}')
                if (
                        response_body.get("resCode") == "00"
                        and response_body.get("cardList")
                        and self.__verify_signature(
                    raw_sign_data, response_body.get("sign")
                )
                ):
                    code_lists = response_body.get("cardList")
                    for code_item in code_lists:
                        codex = self.__decrypt_codex(code_item.get("pincode"))
                        code = self.format_code(
                            serial=code_item.get("serial"),
                            pin=None,
                            codex=codex,
                            expired=code_item.get("expiredate") / 1000,
                        )
                        codes.append(code)

                    # TODO: save log
        except Exception as e:
            logger.exception(e)
            self.after_get_code()
        return codes, {"transaction_id": transaction_id}

    def __check_balance(self, amount):
        signature = self.__generate_signature(self.username)
        request_data = dict(partnerCode=self.username, sign=signature)
        url = "{host}{path}".format(host=self.host, path=self.path_check_balance)
        try:
            response = requests.post(
                url, json=request_data, headers=self.__get_headers()
            )
            response_object = response.json()
            raw_string = "{resCode}{resMessage}{currentBalance}".format(
                resCode=response_object.get("resCode"),
                resMessage=response_object.get("resMessage"),
                currentBalance=response_object.get("currentBalance"),
            )
            if self.__verify_signature(raw_string, response_object.get("sign")):
                if (
                    response_object["resCode"] == "00"
                    and response_object["currentBalance"] >= amount
                ):
                    return True
        except Exception as e:
            logger.exception(e)
        return False

    def __get_body_path_get_code(self, product_code, quantity):
        transaction_id = "{username}_{uuid}".format(
            username=self.username,
            uuid=Utility.generate_uuid(),
        )
        raw_signature = "{username}{transaction_id}{product_code}{quantity}".format(
            username=self.username,
            transaction_id=transaction_id,
            product_code=product_code,
            quantity=quantity,
        )
        signature = self.__generate_signature(raw_signature)
        return dict(
            partnerCode=self.username,
            partnerTransId=transaction_id,
            productCode=product_code,
            quantity=quantity,
            reciever=self.email_receive,
            sign=signature,
        )

    def __get_headers(self):
        return {
            "Content-type": "application/json",
            # 'Authorization': 'Basic ' + constants.URBOX_AUTHORIZATION
        }

    def __generate_signature(self, raw_string):
        private_key = self.__get_private_key()
        return UrboxOpenSsl.generate_signature(private_key, raw_string, True)

    def __verify_signature(self, raw_string, signature):
        public_key = self.__get_public_key()
        return UrboxOpenSsl.verify_signature(public_key, raw_string, signature, True)

    def __decrypt_codex(self, codex):
        private_key = self.__get_private_key()
        decrypt_code = UrboxOpenSsl.rsa_decrypt(private_key, codex)
        return decrypt_code

    def __get_private_key(self):
        return open("/src/app/libs/apis/keys/privKeyForIOMedia.pem", "rb").read()

    def __get_public_key(self):
        return open("/src/app/libs/apis/keys/pubKeyFromIOMedia.pem", "rb").read()

    def get_limit_code(self):
        pass
