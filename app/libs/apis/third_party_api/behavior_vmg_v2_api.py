# coding: utf8
import json
import random
import time
import uuid

from app.redis_distributed_lock import LockConfig, RedisDistributedLock
from loguru import logger
import requests
import redis

import re


from app.const import (
    THIRD_PARTY_VMG_HOST_V2,
    THIRD_PARTY_VMG_PASSWORD_V2,
    THIRD_PARTY_VMG_KEY_BIRTHDAY_TIME_V2,
    THIRD_PARTY_VMG_SOFT_PIN_KEY_V2,
    THIRD_PARTY_VMG_USERNAME_V2,
    THIRD_PARTY_VMG_PRIVATE_KEY_V2,
    THIRD_PARTY_VMG_PUBLIC_KEY_TO_URBOX_V2
)
from app.config import config_app
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import <PERSON>havior<PERSON><PERSON><PERSON><PERSON>
from app.security import U<PERSON><PERSON><PERSON><PERSON><PERSON>, VmgTripleDes

class BehaviorVmgApiV2(Abstract<PERSON><PERSON>, <PERSON>havior<PERSON>ode<PERSON>pi):
    # Class-level Redis connection pool
    _redis_pool = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "VmgApiV2"
        self.username = THIRD_PARTY_VMG_USERNAME_V2
        self.password = THIRD_PARTY_VMG_PASSWORD_V2
        self.key_birthday_time = THIRD_PARTY_VMG_KEY_BIRTHDAY_TIME_V2
        self.soft_pin_key = THIRD_PARTY_VMG_SOFT_PIN_KEY_V2
        self.private_key = THIRD_PARTY_VMG_PRIVATE_KEY_V2
        self.public_key_to_urbox = THIRD_PARTY_VMG_PUBLIC_KEY_TO_URBOX_V2
        self.operation_login = 1400
        self.operation_get_code = 1000
        self.operation_re_download = 1100
        self.api_url = THIRD_PARTY_VMG_HOST_V2 + "/requestHandle"
        self.api_get_balance = THIRD_PARTY_VMG_HOST_V2 + "/queryBalance"

        # Initialize Redis connection pool
        self._init_redis_pool()

        if THIRD_PARTY_VMG_HOST_V2 is None:
            raise Exception("Host chưa được cấu hình")
        if self.username is None:
            raise Exception("Username chưa được cấu hình")
        if self.password is None:
            raise Exception("Password chưa được cấu hình")
        if self.key_birthday_time is None:
            raise Exception("KeyBirthdayTime chưa được cấu hình")
        if self.soft_pin_key is None:
            raise Exception("SoftPinKey chưa được cấu hình")

    @classmethod
    def _init_redis_pool(cls):
        """
        Initialize Redis connection pool (singleton pattern)
        """
        if cls._redis_pool is None:
            try:
                cls._redis_pool = redis.ConnectionPool(
                    host=config_app.REDIS.REDIS_HOST,
                    port=int(config_app.REDIS.REDIS_PORT),
                    db=10,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_timeout=5,
                    socket_connect_timeout=5,
                    health_check_interval=30
                )
                logger.info("Redis connection pool initialized for VMG V2")
            except Exception as e:
                logger.error(f"Failed to initialize Redis pool: {e}")
                raise

    def get_redis_client(self):
        """
        Get Redis client từ connection pool
        """
        try:
            if self._redis_pool is None:
                self._init_redis_pool()
            return redis.Redis(connection_pool=self._redis_pool)
        except Exception as e:
            logger.error(f"Failed to get Redis client: {e}")
            # Fallback to direct connection
            return redis.Redis(
                host=config_app.REDIS.REDIS_HOST,
                port=int(config_app.REDIS.REDIS_PORT),
                db=10,
            )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        product_code = product_info.get("product_code")
        retry_transaction_id = product_info.get("retry_transaction_id")
        supplier_order_id = product_info.get("supplier_order_id")
        codes = []

        token = self.get_token()
        if token is None:
            logger.info('VMG V2 - Không đăng nhập được vào hệ thống')
            return codes, {"message": "Không đăng nhập được vào hệ thống", "transaction_id": ""}

        codes = self.call_api_get_code(
            product_info.get("product_supplier_id"),
            product_info.get("supplier_id"),
            product_id,
            product_code,
            quantity,
            price,
            token,
            product_info.get("product_parent_id"),
            retry_transaction_id,
            supplier_order_id
        )
        return codes

    def call_api_get_code(
            self,
            product_supplier_id,
            supplier_id,
            product_id,
            product_code,
            quantity,
            price,
            token,
            product_parent_id,
            retry_transaction_id,
            supplier_order_id
    ):
        products = []
        if not product_parent_id or product_parent_id == 0:
            return products
        transaction_id = self.before_get_code()
        if retry_transaction_id != '':
            transaction_id = retry_transaction_id
        else:
            supplier_order_id = self.create_order_log(
                product_supplier_id, transaction_id, supplier_id, quantity, product_id
            )
        message = ''
        operation = self.operation_get_code
        if retry_transaction_id != '':
            operation = self.operation_re_download
        signature_params = {
            "username": self.username,
            "requestID": transaction_id,
            "token": token,
            "operation": operation,
        }
        signature_string = "|".join([str(value) for value in signature_params.values()])
        signature = UrBoxRsa.sha256_sign(signature_string, self.private_key)
        params = {
            "operation": operation,
            "username": self.username,
            "signature": signature,
            "requestID": transaction_id,
            "keyBirthdayTime": self.key_birthday_time,
            "token": token
        }
        if retry_transaction_id == '':
            params['buyItems'] = [
                {
                    "productId": product_code,
                    "quantity": quantity,
                }
            ]

        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=self.api_url,
            headers={},
            data=params,
        )
        try:

            response = requests.post(self.api_url, headers={"Content-Type": "application/json"}, data=json.dumps(params))
            response_json = response.json()
            logger.info(f'transaction_id: {transaction_id}, product_id: {product_id}, response: {response.text}')
   
            self.update_logs(
                log=log,
                headers={},
                response_code=response.status_code or 0,
                data= json.loads(response.text) or {},
            )
            if response_json.get("errorCode") == 0 and "products" in response_json:
                if not self.verify_response(response_json.get("signature"), {
                    "errorCode": response_json.get("errorCode"),
                    "requestID": response_json.get("requestID"),
                    "sysTransId": response_json.get("sysTransId"),
                    "token": token
                }):
                    logger.error(f'Signature response không hợp lệ: {response_json}')
                    return [], {
                        "transaction_id": transaction_id,
                        "message": "Signature response không hợp lệ"
                    }
                for product in response_json.get("products"):
                    if "softpins" in product:
                        for item in product.get("softpins"):
                            code = self.get_info_product(item)
                            products.append(code)
                products = self.unique_codex_by_product_parent_id(
                    products, product_parent_id
                )
            message = response_json.get("errorMessage")
        except Exception as e:
            logger.exception(e)
            if log:
                self.update_logs(
                    log=log,
                    response_code=500,
                    headers={},
                    data= {
                        "error": str(e),
                    }
                )
            message = str(e) if str(e) else "Lỗi không xác định"
        quantity_success = len(products)
        quantity_error = quantity - quantity_success
        self.update_order_log(supplier_order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(
            products, supplier_order_id, product_id, price, product_code,transaction_id
        )
        return products, {"transaction_id": transaction_id, "message": message}

    def verify_response(self, signature, response):
        signature_params  = {
            "errorCode": response.get("errorCode", ""),
            "requestID": response.get("requestID", ""),
            "sysTransId": response.get("sysTransId", ""),
            "token": response.get("token", "")
        }
        signature_string = "|".join([str(value) for value in signature_params.values()])
        pem_key = f"-----BEGIN PUBLIC KEY-----\n{self.public_key_to_urbox}\n-----END PUBLIC KEY-----"
        return UrBoxRsa.sha256_verify(signature, signature_string, pem_key)
    
    def get_info_product(self, product):
        try:
            partner_codex_id = product.get("softpinId")
            vmg_expired = product.get("expiryDate")
            vmg_soft_pin_serial = product.get("softpinSerial")
            vmg_soft_pin_code = product.get("softpinPinCode")
           
            key = str(self.soft_pin_key[0:24])
            iv = str(self.soft_pin_key[0:8])
            vmg_codex = VmgTripleDes.decode(
                vmg_soft_pin_code, key, iv
            )
            vmg_codex = re.sub("[^A-Za-z0-9]+", "", vmg_codex)
            expired = Utility.convert_datetime_to_unixtimestamp(
                vmg_expired, "%d/%m/%Y"
            )
            return self.format_code(
                serial=vmg_soft_pin_serial,
                pin=None,
                codex=vmg_codex,
                expired=expired + 86399,
                partner_codex_id = partner_codex_id
            )
        except Exception as e:
            logger.exception(e)
        return None

    def get_token(self):
        """
        Lấy token với distributed lock và connection pooling
        """
        try:
            redis_key = f"vmg_v2_token_{self.username}"
            redis_client = self.get_redis_client()

            # Check token trước khi acquire lock
            token = redis_client.get(redis_key)
            if token:
                logger.info(f"Token found in Redis: {redis_key}")
                return token.decode('utf-8')

            # Sử dụng context manager với optimized config
            lock_config = LockConfig()  # Use optimized defaults
            with RedisDistributedLock(redis_client, self.username, lock_config) as lock:
                # Double-check sau khi có lock
                logger.info("Acquired lock, double-checking token in Redis...")
                token = redis_client.get(redis_key)
                if token:
                    logger.info(f"Token found after lock acquire: {redis_key}")
                    return token.decode('utf-8')

                # Call login API để lấy token mới
                logger.info("No token found, calling login API...")
                token = self._login_and_get_token(redis_client, redis_key, lock)
                return token

        except Exception as e:
            logger.exception(f"Error in get_token: {e}")
            return None

    def _login_and_get_token(self, redis_client, redis_key, lock=None):
        """
        Helper method để call login API và lưu token với lock extension
        """
        try:
            # Auto-extend lock nếu cần thiết trước khi call API
            if lock:
                lock.auto_extend_if_needed()

            signature = UrBoxRsa.sha256_sign(self.username + "|" + self.password, self.private_key)
            params = {
                "requestID": self.create_transaction_id('UBL-'),
                "operation": self.operation_login,
                "username": self.username,
                "merchantPass": self.password,
                "signature": signature
            }

            logger.info(f"Calling login API for user: {self.username}")
            response = requests.post(
                self.api_url,
                headers={"Content-Type": "application/json"},
                data=json.dumps(params),
                timeout=30  # Add timeout
            )
            response_json = response.json()

            logger.info(f"Login API response: {response_json}")

            if response_json.get("errorCode") == 0:
                token = response_json.get("token")
                if token:
                    # Lưu token với TTL 13 giờ
                    redis_client.setnx(redis_key, token)
                    redis_client.expire(redis_key, 3600 * 13)
                    logger.info(f"Token saved to Redis: {redis_key}")
                    return token
                else:
                    logger.error("Login successful but no token in response")
            else:
                error_msg = response_json.get("errorMessage", "Unknown error")
                logger.error(f"Login API failed: {error_msg}")

        except requests.RequestException as e:
            logger.error(f"Network error during login: {e}")
        except Exception as e:
            logger.exception(f"Unexpected error during login: {e}")

        return None

    def get_balance(self):
        try:
            token = self.get_token()
            if token is None:
                return {
                    "message": "Không lấy được token",
                    "error_code": 500
                }
            params = {
                "username": self.username,
                "merchantPass": self.password,
                "token": token,
            }
            response = requests.post(self.api_get_balance, headers={"Content-Type": "application/json"}, data=json.dumps(params))
            response_json = response.json()
            return response_json
        except Exception as e:
            logger.exception(e)
            return {
                "message": str(e),
                "error_code": 500
            }