# coding: utf8
import time
from datetime import datetime


import app.const as app_const

from loguru import logger

import requests

from app.const import PROCESS_SENT

from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import AbstractApi
from app.libs.apis.third_party_api.behavior_code_api import Be<PERSON>vior<PERSON><PERSON><PERSON>pi
from app.helper import Helper
from app.repositories.log_request_api import log_request_api_repo
from app.services.rate_limiter import rate_limiter
from app.utils import set_rate_limit_per_minute, raw_response_message


class BehaviorGrabApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "GrabApi"
        self.client_id = app_const.GRAB_CLIENT_ID
        self.client_secret = app_const.GRAB_CLIENT_SECRET
        self.grand_type = app_const.GRAB_GRANT_TYPE
        self.scope = app_const.GRAB_SCOPE
        self.host = app_const.GRAB_HOST
        self.path_get_code = app_const.GRAB_PATH_BUY_CODE
        self.path_authen = app_const.GRAB_PATH_AUTH
        self.path_gift_links = app_const.GRAB_PATH_GIFT_LINKS
        self.expire_token = 0
        self.token = ""
        self.RATE_LIMIT_PER_MINUTE = 49


        self.validate_api_config(
            path_get_code=self.path_get_code,
            path_authen=self.path_authen,
            client_id=self.client_id,
            client_secret=self.client_secret,
            host=self.host,
            grand_type=self.grand_type,
            scope=self.scope
        )

    def get_code(self, product_info):
        logger.info(product_info)
        self.validate_get_code(product_info)
        quantity = product_info.get("quantity")
        product_id = product_info.get("product_id")
        price = product_info.get("price")
        if quantity > product_info.get('max_codes_per_call'):
            quantity = product_info.get('max_codes_per_call')
        rate_limiter.wait_if_needed(request_name=self.title, limit_per_minute=self.RATE_LIMIT_PER_MINUTE)
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_info.get("product_supplier_id"),
            transaction_id,
            product_info.get("supplier_id"),
            quantity,
            product_id,
            product_info.get("po_code"),
        )
        if product_info.get("effective_date") and product_info.get("effective_date") <= time.time():
            error_time_message = f"effective_date phải lớn hơn ngày hiện tại. Current value: {product_info.get('effective_date')}"
            logger.warning(error_time_message)
            # return [], error_time_message
        if product_info.get("expired") and product_info.get("expired") <= time.time():
            error_time_message = f"expired phải lớn hơn ngày hiện tại. Current value: {product_info.get('expired')}"
            logger.warning(error_time_message)
            return [], {"transaction_id": transaction_id, "message":  error_time_message}
        if product_info.get("effective_date") and product_info.get("expired") and product_info.get(
                "effective_date") > product_info.get("expired"):
            error_time_message = f"effective_date phải nhỏ hơn expired. Current value: {product_info.get('effective_date')}, expired: {product_info.get('expired')}"
            logger.warning(error_time_message)
            return [], {"transaction_id": transaction_id, "message":  error_time_message}

        body = self.__get_body_path_get_code(transaction_id, {
            "value": price,
            "quantity": quantity,
            "start_date": Utility.convert_unixtime_to_format_time(
                product_info.get("effective_date"), "%Y-%m-%dT%H:%M:%SZ"
            ) if product_info.get("effective_date") else None,
            "end_date": Utility.convert_unixtime_to_format_time(
                product_info.get("expired"), "%Y-%m-%dT%H:%M:%SZ"
            ) if product_info.get("expired") else None,
            'product_code': product_info.get('product_code')
        })
        logger.info(f'body get code: {body}')
        headers = self.__get_headers()
        url = "{host}{path}".format(host=self.host, path=self.path_get_code)
        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
            request_round=product_info.get('request_round') or None,
            contract_code=product_info.get("contract_code")
        )
        order_detail_log = {
            "order_id": product_info.get("order_id") or 0,
            "supplier_order_id": order_id or 0,
            "request": str(body),
            "transaction_id": transaction_id,
            "request_url": url,
            "quantity": quantity,
            "header": str(headers)
        }

        try:
            response = requests.post(url, json=body, headers=headers)
            logger.info(f'GRAB_RESPONSE: {response.content.decode()}')
            if log is not None:
                self.update_logs(
                    log=log,
                    headers=response.headers,
                    response_code=response.status_code,
                    data=dict({
                        "response_buy": raw_response_message(response)
                    }),
                )
            if not response:
                order_detail_log.update({
                    "response": "Không có response",
                    "http_code": -1,
                    "action": app_const.PROCESS_FAIL
                })
                self.save_order_detail_log(**order_detail_log)
                Utility.send_telegram_message_to_group(
                    message=f"Product {product_info.get('product_id')}-{product_info.get('product_code')} không lấy được code từ API. OrderId: {order_id}."
                )
                return [],{"transaction_id": transaction_id, "message":  f"Xảy ra lỗi khi lấy code Grab - {order_id}"}

            order_detail_log.update({
                "http_code": response.status_code,
                'response': response.content.decode(),
            })
            if response.status_code != 200:
                order_detail_log.update({
                    "action": app_const.PROCESS_FAIL
                })
                self.save_order_detail_log(**order_detail_log)

                res = response.json()
                err_msg = res.get("message") if hasattr(res, 'message') else ""
                Utility.send_telegram_message_to_group(
                    message=f"Product {product_info.get('product_id')}-{product_info.get('product_code')} không lấy được code từ API. OrderId: {order_id}. Lỗi : {err_msg}"
                )
                logger.error(response.json())
                return [], {"transaction_id": transaction_id, "message":  f"Xảy ra lỗi khi lấy code Grab - {order_id}: {err_msg}"}

            if response and response.status_code == 200:
                response_body = response.json()
                if response_body:
                    order_code = response_body.get("orderID")
                    reference = response_body.get("reference")
                    logger.info(f"order_id: {order_code}, reference: {reference}")
                    order_detail_log.update({
                        "order_code": order_code,
                        "action": PROCESS_SENT
                    })

            self.save_order_detail_log(**order_detail_log)
        except Exception as e:
            logger.exception(e)
            order_detail_log.update({
                "response": str(e),
                "http_code": e.code if hasattr(e, 'code') else 400,
                "action": app_const.PROCESS_FAIL
            })
            self.save_order_detail_log(**order_detail_log)
            self.after_get_code()
            Utility.send_telegram_message_to_group(
                message=f"Product {product_info.get('product_id')}-{product_info.get('product_code')} không lấy được code từ API. OrderId: {order_id}. Lỗi : {str(e)}"
            )
            return [], {"transaction_id": transaction_id, "message":  f"Xảy ra lỗi khi lấy code Grab - {order_id}: {str(e)}"}

        self.update_order_log(order_id, 0, 0)
        return [], {"transaction_id": transaction_id, "message": 'Success'}

    def __get_body_path_get_code(self, transaction_id, product_info):
        body = {
            "type": "Awesome Gifts Partner Inc.",
            "reference": transaction_id,
            "idempotencyKey": transaction_id,
            "order": {
                "senderAlias": "GrabGift",
                "gift": {
                    "value": product_info.get("value"),
                    "quantity": product_info.get("quantity"),
                    "countryCode": "VN",
                    "inventories": product_info.get("product_code").split(','),
                    "designKey": "thank_you",
                    "messageTitle": "Congratulations on your gift!",
                    "messageBody": "Hope you enjoy your GrabGift to spend on Grab services",
                    "enableShortCode": True
                }
            }
        }
        gift_item = body.get("order").get("gift")
        if product_info.get("start_date"):
            gift_item.update({
                "startDate": product_info.get("start_date")
            })
        if product_info.get("end_date"):
            gift_item.update({
                "endDate": product_info.get("end_date")
            })

        body.get('order').update({
            "gift": gift_item
        })
        return body

    def __get_headers(self):
        if self.expire_token < Helper.get_now_unix_timestamp() - 10:
            self.get_token()

        return {
            "Content-type": "application/json",
            "Authorization": f"Bearer: {self.token}"
        }

    def __get_auth_header(self):
        return {
            "Content-type": "application/x-www-form-urlencoded",
        }

    def get_gift_links(self, order_id, transaction_id):
        codes = []
        path = self.path_gift_links.format(grabGiftOrderID=order_id)
        url = "{host}{path}".format(host=self.host, path=path)
        rate_limiter.wait_if_needed(request_name=self.title, limit_per_minute=self.RATE_LIMIT_PER_MINUTE)
        headers = self.__get_headers()
        error_message = ''
        try:
            logger.info(f'start get gift link transaction {transaction_id}, order_id: {order_id}...')
            logger.info(url)
            logger.info(headers)
            response = requests.get(url, headers=headers)

            log = log_request_api_repo.get_by_transaction(transaction_id=transaction_id, merchant_name=self.title)
            if log:
                response_log = log.response_data or {}
                response_log['response_data']['response_gift_codes'] = raw_response_message(response)
                log.response_data = response_log
                log.response_code = response.status_code or response_log['response_code']
                log.updated = datetime.utcnow()
                log.save()

            logger.info(f'GRAB_GET_GIFT_LINKS: transaction_id: {transaction_id} - {response.status_code or "N/A"}')
            if response and response.status_code == 200:
                response_body = response.json()
                if len(response_body.get("gifts")) > 0:
                    gifts = response_body.get("gifts")
                    for gift in gifts:
                        code = self.format_code(
                            serial=None,
                            pin=None,
                            codex=gift.get("short_code"),
                            expired=Utility.convert_format_time_to_unixtime(
                                gift.get("end_date"), "%Y-%m-%dT%H:%M:%SZ"
                            ),
                            partner_codex_id=gift.get("uuid")
                        )
                        code.update({
                            "value": gift.get("value")
                        })
                        codes.append(code)
                    return codes, {"transaction_id": transaction_id, "message": '', 'partner_transaction_id': order_id}
                else:
                    error_message = response_body.get("message") or "Không lấy được gift links !!!"
            else:
                error_message = "Không lấy được gift links"
            return [], {"transaction_id": transaction_id, "message": error_message}
        except Exception as e:
            logger.exception(e)
            return [], {"transaction_id": transaction_id, "message":  str(e)}

    def get_token(self):
        url = "{host}{path}".format(host=self.host, path=self.path_authen)
        body = {
            "client_id": self.client_id,
            "grant_type": self.grand_type,
            "client_secret": self.client_secret,
            "scope": self.scope,
        }

        headers = self.__get_auth_header()
        try:
            logger.info('start get token...')
            logger.info(url)
            logger.info(body)
            logger.info(headers)
            response = requests.post(url, data=body, headers=headers)
            logger.info(f'GRAB_GET_TOKEN: {response.json()}')
            if response and response.status_code == 200:
                response_body = response.json()
                if response_body.get("access_token"):
                    access_token = response_body.get("access_token")
                    self.token = access_token
                    self.expire_token = response_body.get("expires_in") + int(time.time())
                    return
            raise Exception(f'{self.title}: Không lấy được token!')
        except Exception as e:
            logger.exception(e)
            raise Exception(f'{self.title}: Exception không lấy được token')

    def get_balance(self):
        url = "{host}{path}".format(host=self.host, path='/gifts/partner/account/balance')
        headers = self.__get_headers()
        try:
            response = requests.get(url, headers=headers)
            return response.json()
        except Exception as e:
            logger.exception(e)
            raise Exception(f'{self.title}: Exception không lấy được balance')