# coding: utf8
from app.const import (
    THIRD_PARTY_EZIN_HOST,
    THIRD_PARTY_EZIN_PATH_BUY_CODE,
    THIRD_PARTY_EZIN_TOKEN,
)
from app.libs.base.utility import Utility
from app.libs.apis.third_party_api.abstract_api import Abstract<PERSON>pi
from app.libs.apis.third_party_api.behavior_code_api import BehaviorCodeApi

import hashlib
import logging
import requests

logger = logging.getLogger(__name__)


class BehaviorEzinApi(AbstractApi, BehaviorCodeApi):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.title = "EzinApi"
        self.host = THIRD_PARTY_EZIN_HOST
        self.path_get_code = THIRD_PARTY_EZIN_PATH_BUY_CODE
        self.token = THIRD_PARTY_EZIN_TOKEN
        self.validate_api_config(
            host=self.host, path_get_code=self.path_get_code, token=self.token
        )

    def get_code(self, product_info):
        self.validate_get_code(product_info)
        list_code = []
        transaction_id = self.before_get_code()
        order_id = self.create_order_log(
            product_supplier_id=product_info.get("product_supplier_id"),
            transaction_id=transaction_id,
            supplier_id=product_info.get("supplier_id"),
            quantity=product_info.get("quantity"),
            product_id=product_info.get("product_id"),
        )
        headers = self.generate_headers()
        body = self.generate_body(transaction_id, product_info.get("product_code"), product_info.get("quantity"))
        url = f"{self.host}/{self.path_get_code}"
        log = self.create_logs(
            transaction_id=transaction_id,
            merchant_name=self.title,
            url=url,
            headers=headers,
            data=body,
            contract_code=product_info.get("contract_code")
        )
        try:
            response = requests.post(url, json=body, headers=headers)
            self.update_logs(
                log=log,
                headers=response.headers,
                response_code=response.status_code,
                data=response.json(),
            )
            if response and response.status_code == 200:
                response_data = response.json()
                list_response_code = self.get_response_list_code(response_data)
                expired = self.get_expired(response_data)
                for response_code in list_response_code:
                    list_code.append(self.format_code(
                        serial=None,
                        pin=None,
                        codex=response_code,
                        expired=expired,
                    ))
                list_code = self.unique_codex_by_product_parent_id(
                    list_code, product_info.get("product_parent_id")
                )
            else:
                Utility.send_telegram_message_to_group(
                    message=f"EzinApi get code failed: {response.status_code} - {response.text}"
                )
        except Exception as e:
            logger.exception(e)
            Utility.send_telegram_message_to_group(
                message=f"EzinApi get code Exception: {str(e)}"
            )
        quantity_success = len(list_code)
        quantity_error = product_info.get("quantity") - quantity_success
        self.update_order_log(order_id, quantity_success, quantity_error)
        self.creater_order_detail_log(
            codes=list_code,
            order_id=order_id,
            product_id=product_info.get("product_id"),
            money=product_info.get("price"),
            product_code=product_info.get("product_code")
        )
        return list_code, {"transaction_id": transaction_id}

    def generate_headers(self):
        return {"Content-type": "application/json", "Authorization": f"Bearer {self.token}"}

    def generate_body(self, transaction_id, product_code, quantity):
        return {
            "request_id": transaction_id,
            "package_code": product_code,
            "quantity": quantity,
            "sign": self.generate_sign(transaction_id)
        }

    def generate_sign(self, transaction_id):
        return hashlib.md5(f"{self.token}{transaction_id}".encode("utf-8")).hexdigest()

    def get_response_list_code(self, response_data):
        if response_data.get("response_code") == "00" and response_data.get("data"):
            list_code = response_data.get("data").get("unique_links")
            return list_code or []
        return []

    def get_expired(self, response_data):
        if response_data.get("response_code") == "00" and response_data.get("data"):
            expired = response_data.get("data").get("expired_date")
            return Utility.convert_datetime_to_unixtimestamp(expired, "%d/%m/%Y")
        return 0

    def get_limit_code(self):
        pass
