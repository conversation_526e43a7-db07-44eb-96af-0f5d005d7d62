#!/usr/bin/python
# -*- coding: utf-8 -*-

from app.const import (
    SERVICE_DOMAIN_URCARD, SERVICE_DOMAIN_URCARD_ACTION_ORDER, SERVICE_DOMAIN_URCARD_ACCESS_TOKEN,
    SERVICE_DOMAIN_URCARD_PROFILE_ID,
)

from app.libs.base.request_dao import RequestDao
from app.libs.apis.abstract_aapi import AbstractAApi
from loguru import logger

request_dao = RequestDao()


class UrcardAPI(AbstractAApi):
    @staticmethod
    def save(*args, **kwargs):
        url = "{domain}/{action}".format(
            domain=SERVICE_DOMAIN_URCARD,
            action=SERVICE_DOMAIN_URCARD_ACTION_ORDER,
        )
        orders = kwargs.get("orders")
        orders['quantity_success'] = orders.get('quantity', 0)
        if orders and orders['quantity_failed'] > 0:
            orders['quantity_success'] = orders['quantity'] - orders['quantity_failed']
        body = {
            "request_id": kwargs.get("request_id"),
            "po": kwargs.get("po_code"),
            "orders": orders,
            "successful": kwargs.get("successful") and orders['quantity_failed'] == 0,
            "message": kwargs.get("message"),
        }
        headers = {
            "Content-Type": "application/json",
            "x-profile-id": SERVICE_DOMAIN_URCARD_PROFILE_ID,
            "x-access-token": SERVICE_DOMAIN_URCARD_ACCESS_TOKEN,
        }

        logger.info(f'UrcardAPI:callback_order:headers: {headers}')
        try:
            response = request_dao.patch(
                url, params=body, headers=headers
            )
            response_message = response.text
            logger.info(f'UrcardAPI:callback_order:request: {body},response: {response.text}')
        except Exception as e:
            response_message = str(e)
            logger.info(f'UrcardAPI:callback_order:request_exception:{body},response: {str(e)}')
        return {
            "request": {
                "request_id": kwargs.get("request_id"),
                "po": kwargs.get("po_code"),
                "successful": kwargs.get("successful"),
                "message": kwargs.get("message"),
            },
            "response": response_message,
        }
