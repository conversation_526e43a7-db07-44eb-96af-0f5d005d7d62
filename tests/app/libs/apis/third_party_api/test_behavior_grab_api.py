#!/usr/bin/python
# -*- coding: utf-8 -*-
import pytest
import time
from unittest.mock import patch, MagicMock
import requests

from app.libs.apis.third_party_api.behavior_grab_api import Behavior<PERSON>rabApi
from app.libs.base.utility import Utility
from app.const import PROCESS_FAIL


class TestBehaviorGrabApi:
    # Fixtures
    @pytest.fixture
    def base_product_info(self):
        return {
            "product_id": 10328,
            "supplier_id": 191,
            "product_supplier_id": 2383,
            "code_prefix": "UB",
            "code_length": 9,
            "scheme_code": "",
            "po_code": "MPO55942",
            "order_id": 2671,
            "quantity": 1,
            "price": 10000,
            "product_parent_id": 35,
            "product_code": "grab_food",
            "effective_date": None,
            "expired": None,
            "max_codes_per_call": 1000,
            "retry_transaction_id": "",
            "meta_data": None,
            "po_request_id": "MPO1745482654",
            "supplier_order_id": 0,
            "request_round": "product-MPO55942-10328-round-1"
        }
    
    @pytest.fixture
    def mock_response_success(self):
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"orderID": "test_order_id", "reference": "test_reference"}
        return mock_response
    
    @pytest.fixture
    def mock_response_error(self):
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {"message": "Error message"}
        return mock_response
    
    def setup_common_mocks(self, mocker, transaction_id="test_transaction_id", order_id=123):
        # Mock các dependencies
        mocker.patch.object(BehaviorGrabApi, 'validate_get_code')
        mocker.patch.object(BehaviorGrabApi, 'create_order_log', return_value=order_id)
        mocker.patch('app.utils.set_rate_limit_per_minute')
        mocker.patch.object(BehaviorGrabApi, 'create_logs')
        mocker.patch.object(BehaviorGrabApi, 'update_logs')
        mocker.patch.object(BehaviorGrabApi, 'save_order_detail_log')
        mocker.patch.object(BehaviorGrabApi, 'update_order_log')
        mocker.patch.object(BehaviorGrabApi, '_BehaviorGrabApi__get_headers', return_value={})
        
        # Không mock before_get_code theo yêu cầu
        # Nhưng chúng ta cần đảm bảo nó trả về một giá trị cố định cho các test
        mocker.patch.object(BehaviorGrabApi, 'before_get_code', return_value=transaction_id)
        
        return transaction_id, order_id
    
    # Test với params chuẩn
    def test_get_code_with_standard_params(self, mocker, base_product_info, mock_response_success):
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(base_product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        assert call_args[0] == transaction_id  # transaction_id
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("value") == base_product_info.get("price")
        assert product_info_arg.get("quantity") == base_product_info.get("quantity")
        assert product_info_arg.get("start_date") is None
        assert product_info_arg.get("end_date") is None
        assert product_info_arg.get("product_code") == base_product_info.get("product_code")
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    # Test với các biến thể của quantity
    @pytest.mark.parametrize("quantity,expected", [
        (1, 1),  # Quantity = 1
        (500, 500),  # Quantity < max_codes_per_call
        (1500, 1000)  # Quantity > max_codes_per_call
    ])
    def test_get_code_with_different_quantities(self, mocker, base_product_info, mock_response_success, quantity, expected):
        # Chuẩn bị dữ liệu test
        product_info = base_product_info.copy()
        product_info["quantity"] = quantity
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("quantity") == expected
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    # Test với các biến thể của price
    @pytest.mark.parametrize("price", [10000, 20000, 50000])
    def test_get_code_with_different_prices(self, mocker, base_product_info, mock_response_success, price):
        # Chuẩn bị dữ liệu test
        product_info = base_product_info.copy()
        product_info["price"] = price
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("value") == price
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    # Test với các biến thể của product_code
    @pytest.mark.parametrize("product_code,expected_str", [
        (None, ""),  # Null
        ("", ""),  # Chuỗi rỗng
        (12345, "12345"),  # Số
        ("random_string", "random_string"),  # Chuỗi ngẫu nhiên
        ("grab_food", "grab_food"),  # Giá trị chuẩn
        ("code1,code2,code3", "code1,code2,code3")  # Nhiều giá trị
    ])
    def test_get_code_with_different_product_codes(self, mocker, base_product_info, mock_response_success, product_code, expected_str):
        # Chuẩn bị dữ liệu test
        product_info = base_product_info.copy()
        product_info["product_code"] = product_code
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("product_code") == str(product_code) if product_code is not None else None
        
        # Kiểm tra inventories trong body
        body = spy_get_body.spy_return
        expected_inventories = expected_str.split(',') if expected_str else [""]
        assert body["order"]["gift"]["inventories"] == expected_inventories
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    # Test với các biến thể của effective_date và expired
    def test_get_code_with_null_dates(self, mocker, base_product_info, mock_response_success):
        # Chuẩn bị dữ liệu test - cả hai đều null
        product_info = base_product_info.copy()
        product_info["effective_date"] = None
        product_info["expired"] = None
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("start_date") is None
        assert product_info_arg.get("end_date") is None
        
        # Kiểm tra body không có startDate và endDate
        body = spy_get_body.spy_return
        assert "startDate" not in body["order"]["gift"]
        assert "endDate" not in body["order"]["gift"]
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    def test_get_code_with_effective_date_only(self, mocker, base_product_info, mock_response_success):
        # Chuẩn bị dữ liệu test - chỉ có effective_date
        current_time = int(time.time())
        future_time = current_time + 3600  # 1 giờ sau
        
        product_info = base_product_info.copy()
        product_info["effective_date"] = future_time
        product_info["expired"] = None
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Mock Utility.convert_unixtime_to_format_time
        expected_date_str = "2023-01-01T12:00:00Z"
        mocker.patch.object(
            Utility, 
            'convert_unixtime_to_format_time', 
            return_value=expected_date_str
        )
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("start_date") == expected_date_str
        assert product_info_arg.get("end_date") is None
        
        # Kiểm tra body có startDate nhưng không có endDate
        body = spy_get_body.spy_return
        assert body["order"]["gift"]["startDate"] == expected_date_str
        assert "endDate" not in body["order"]["gift"]
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    def test_get_code_with_expired_only(self, mocker, base_product_info, mock_response_success):
        # Chuẩn bị dữ liệu test - chỉ có expired
        current_time = int(time.time())
        future_time = current_time + 86400  # 1 ngày sau
        
        product_info = base_product_info.copy()
        product_info["effective_date"] = None
        product_info["expired"] = future_time
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Mock Utility.convert_unixtime_to_format_time
        expected_date_str = "2023-01-02T12:00:00Z"
        mocker.patch.object(
            Utility, 
            'convert_unixtime_to_format_time', 
            return_value=expected_date_str
        )
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("start_date") is None
        assert product_info_arg.get("end_date") == expected_date_str
        
        # Kiểm tra body không có startDate nhưng có endDate
        body = spy_get_body.spy_return
        assert "startDate" not in body["order"]["gift"]
        assert body["order"]["gift"]["endDate"] == expected_date_str
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    def test_get_code_with_valid_dates(self, mocker, base_product_info, mock_response_success):
        # Chuẩn bị dữ liệu test - cả hai đều có giá trị hợp lệ
        current_time = int(time.time())
        effective_time = current_time + 3600  # 1 giờ sau
        expired_time = current_time + 86400  # 1 ngày sau
        
        product_info = base_product_info.copy()
        product_info["effective_date"] = effective_time
        product_info["expired"] = expired_time
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Mock Utility.convert_unixtime_to_format_time
        def mock_convert_time(timestamp, format_str):
            if timestamp == effective_time:
                return "2023-01-01T12:00:00Z"
            elif timestamp == expired_time:
                return "2023-01-02T12:00:00Z"
            return None
        
        mocker.patch.object(
            Utility, 
            'convert_unixtime_to_format_time', 
            side_effect=mock_convert_time
        )
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        call_args = spy_get_body.call_args[0]
        
        product_info_arg = call_args[1]
        assert product_info_arg.get("start_date") == "2023-01-01T12:00:00Z"
        assert product_info_arg.get("end_date") == "2023-01-02T12:00:00Z"
        
        # Kiểm tra body có cả startDate và endDate
        body = spy_get_body.spy_return
        assert body["order"]["gift"]["startDate"] == "2023-01-01T12:00:00Z"
        assert body["order"]["gift"]["endDate"] == "2023-01-02T12:00:00Z"
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    def test_get_code_with_invalid_dates(self, mocker, base_product_info):
        # Chuẩn bị dữ liệu test - effective_date > expired
        current_time = int(time.time())
        effective_time = current_time + 86400  # 1 ngày sau
        expired_time = current_time + 3600  # 1 giờ sau
        
        product_info = base_product_info.copy()
        product_info["effective_date"] = effective_time
        product_info["expired"] = expired_time
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        
        # Mock Utility.convert_unixtime_to_format_time
        def mock_convert_time(timestamp, format_str):
            if timestamp == effective_time:
                return "2023-01-02T12:00:00Z"
            elif timestamp == expired_time:
                return "2023-01-01T12:00:00Z"
            return None
        
        mocker.patch.object(
            Utility, 
            'convert_unixtime_to_format_time', 
            side_effect=mock_convert_time
        )
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert len(result) == 0
        assert "effective_date phải nhỏ hơn expired" in message
        
        # Kiểm tra requests.post không được gọi
        assert not hasattr(requests, 'post.called')
    
    def test_get_code_with_past_expired(self, mocker, base_product_info):
        # Chuẩn bị dữ liệu test - expired < thời gian hiện tại
        current_time = int(time.time())
        expired_time = current_time - 3600  # 1 giờ trước
        
        product_info = base_product_info.copy()
        product_info["effective_date"] = None
        product_info["expired"] = expired_time
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        
        # Mock time.time để trả về current_time
        mocker.patch('time.time', return_value=current_time)
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert len(result) == 0
        assert "expired phải lớn hơn ngày hiện tại" in message
        
        # Kiểm tra requests.post không được gọi
        assert not hasattr(requests, 'post.called')
    
    # Test với po_code
    def test_get_code_with_po_code(self, mocker, base_product_info, mock_response_success):
        # Chuẩn bị dữ liệu test
        product_info = base_product_info.copy()
        product_info["po_code"] = "MPO55942"
        
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_success)
        
        # Spy vào __get_body_path_get_code để kiểm tra tham số
        spy_get_body = mocker.spy(BehaviorGrabApi, '_BehaviorGrabApi__get_body_path_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(product_info)
        
        # Kiểm tra kết quả
        assert message == "Success"
        assert result == []
        
        # Kiểm tra __get_body_path_get_code được gọi với đúng tham số
        spy_get_body.assert_called_once()
        
        # Kiểm tra po_code không được sử dụng trực tiếp trong body
        body = spy_get_body.spy_return
        assert "po_code" not in str(body)
        
        # Kiểm tra requests.post được gọi
        requests.post.assert_called_once()
    
    # Test khi API trả về lỗi
    def test_get_code_api_error(self, mocker, base_product_info, mock_response_error):
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        mocker.patch('requests.post', return_value=mock_response_error)
        
        # Mock Utility.send_telegram_message_to_group
        mocker.patch.object(Utility, 'send_telegram_message_to_group')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(base_product_info)
        
        # Kiểm tra kết quả
        assert len(result) == 0
        assert f"Xảy ra lỗi khi lấy code Grab - {order_id}" in message
        
        # Kiểm tra save_order_detail_log được gọi với action=PROCESS_FAIL
        grab_api.save_order_detail_log.assert_called_once()
        call_kwargs = grab_api.save_order_detail_log.call_args[1]
        assert call_kwargs.get("action") == PROCESS_FAIL
        
        # Kiểm tra Utility.send_telegram_message_to_group được gọi
        Utility.send_telegram_message_to_group.assert_called_once()
    
    # Test khi có exception xảy ra
    def test_get_code_exception(self, mocker, base_product_info):
        # Setup mocks
        transaction_id, order_id = self.setup_common_mocks(mocker)
        
        # Mock requests.post để raise exception
        mocker.patch('requests.post', side_effect=Exception("Test exception"))
        
        # Mock Utility.send_telegram_message_to_group
        mocker.patch.object(Utility, 'send_telegram_message_to_group')
        
        # Mock after_get_code
        mocker.patch.object(BehaviorGrabApi, 'after_get_code')
        
        # Gọi function cần test
        grab_api = BehaviorGrabApi()
        result, message = grab_api.get_code(base_product_info)
        
        # Kiểm tra kết quả
        assert len(result) == 0
        assert f"Xảy ra lỗi khi lấy code Grab - {order_id}" in message
        assert "Test exception" in message
        
        # Kiểm tra save_order_detail_log được gọi với action=PROCESS_FAIL
        grab_api.save_order_detail_log.assert_called_once()
        call_kwargs = grab_api.save_order_detail_log.call_args[1]
        assert call_kwargs.get("action") == PROCESS_FAIL
        
        # Kiểm tra Utility.send_telegram_message_to_group được gọi
        Utility.send_telegram_message_to_group.assert_called_once()
        
        # Kiểm tra after_get_code được gọi
        grab_api.after_get_code.assert_called_once()
