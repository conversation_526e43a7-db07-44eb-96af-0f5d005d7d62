#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time
import redis
import sys
import os
from unittest.mock import patch, MagicMock

# Thêm thư mục gốc của dự án vào Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Tạo mock cho các module không cần thiết
sys.modules['orm_alchemy'] = MagicMock()
sys.modules['kafka_app'] = MagicMock()
sys.modules['mongoengine'] = MagicMock()
sys.modules['elasticapm.contrib.flask'] = MagicMock()

# Tạo mock cho config_app
mock_config = MagicMock()
mock_config.REDIS = MagicMock()
mock_config.REDIS.REDIS_HOST = 'localhost'
mock_config.REDIS.REDIS_PORT = '6379'
mock_config.REDIS.REDIS_DB = '0'
sys.modules['app.config'] = MagicMock()
sys.modules['app.config'].config_app = mock_config

# Kết nối đến Redis
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0
)

# Import app.extensions và ghi đè redis_client
import app.extensions
app.extensions.redis_client = redis_client

# Bây giờ có thể import function gốc từ app.utils
from app.utils import set_rate_limit_per_minute

# Thêm hàm debug để in thông tin
def debug_rate_limit(product_id, request_per_minute=50):
    """Wrapper cho hàm set_rate_limit_per_minute để in thông tin debug"""
    # Lấy thông tin trước khi gọi hàm
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    current_count = redis_client.zcard(lock_key)
    print(f"Current count: {current_count}/{request_per_minute}")

    # Lưu thời gian bắt đầu
    start_time = time.time()

    # Gọi hàm gốc
    set_rate_limit_per_minute(product_id, request_per_minute)

    # Tính thời gian thực hiện
    execution_time = time.time() - start_time

    # Kiểm tra xem có bị rate limit không
    # Nếu thời gian thực hiện lớn hơn 0.1 giây, có thể đã có sleep
    was_rate_limited = execution_time > 0.1

    # Kiểm tra số lượng request trong Redis
    new_count = redis_client.zcard(lock_key)

    # In thông tin
    if was_rate_limited:
        print(f"Rate limited! Execution time: {execution_time:.2f} seconds")
    else:
        print(f"Not rate limited. Execution time: {execution_time:.2f} seconds")
    print(f"Redis count: {current_count} -> {new_count}")

    return was_rate_limited

# Kiểm tra chức năng rate limiting trong thực tế

# Test case 1: Basic rate limiting
def test_basic_rate_limiting():
    """Kiểm tra chức năng rate limiting cơ bản"""
    # Xóa key cũ nếu có
    product_id = "test_basic_rate_limiting"
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    redis_client.delete(lock_key)

    request_per_minute = 5  # Giới hạn 5 request/phút

    print(f"\n=== Test 1: Basic Rate Limiting ===\n")
    print(f"Testing rate limit with {request_per_minute} requests per minute")
    print("Sending 10 requests in quick succession...")

    start_time = time.time()
    rate_limited_count = 0

    for i in range(10):
        print(f"\nRequest {i+1}...")
        was_rate_limited = debug_rate_limit(product_id, request_per_minute)
        if was_rate_limited:
            rate_limited_count += 1

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\nResults:")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Rate limited requests: {rate_limited_count}")
    print(f"Average time per request: {total_time/10:.2f} seconds")

    # Kiểm tra số lượng request trong Redis
    count = redis_client.zcard(lock_key)
    print(f"Number of requests in Redis: {count}")

    # Hiển thị các timestamp
    entries = redis_client.zrange(lock_key, 0, -1, withscores=True)
    print(f"Timestamps in Redis: {entries}")

    # Kiểm tra xem có ít nhất 5 request bị rate limit không
    assert rate_limited_count >= 5, f"Expected at least 5 rate limited requests, got {rate_limited_count}"


# Test case 2: Rate limiting with different request_per_minute values
def test_different_rate_limits():
    """Kiểm tra chức năng rate limiting với các giá trị request_per_minute khác nhau"""
    print(f"\n=== Test 2: Different Rate Limits ===\n")

    # Danh sách các giá trị request_per_minute cần kiểm tra
    rate_limits = [1, 10, 50, 100]

    for rpm in rate_limits:
        # Xóa key cũ nếu có
        product_id = f"test_rate_limit_{rpm}"
        lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
        redis_client.delete(lock_key)

        print(f"\nTesting with request_per_minute = {rpm}")

        # Gọi hàm 3 lần
        for i in range(3):
            was_rate_limited = debug_rate_limit(product_id, rpm)
            print(f"Request {i+1}: {'Rate limited' if was_rate_limited else 'Not rate limited'}")

        # Kiểm tra số lượng request trong Redis
        count = redis_client.zcard(lock_key)
        print(f"Number of requests in Redis: {count}")

        # Kiểm tra xem số lượng request có đúng không
        assert count <= rpm, f"Expected at most {rpm} requests in Redis, got {count}"


# Test case 3: Rate limiting with request_per_minute = 0
def test_zero_rate_limit():
    """Kiểm tra chức năng rate limiting với request_per_minute = 0"""
    print(f"\n=== Test 3: Zero Rate Limit ===\n")

    # Xóa key cũ nếu có
    product_id = "test_zero_rate_limit"
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    redis_client.delete(lock_key)

    request_per_minute = 0  # Không giới hạn

    print(f"Testing with request_per_minute = {request_per_minute}")

    # Gọi hàm 5 lần
    for i in range(5):
        print(f"\nRequest {i+1}:")
        was_rate_limited = debug_rate_limit(product_id, request_per_minute)

    # Kiểm tra xem key có tồn tại trong Redis không
    exists = redis_client.exists(lock_key)
    print(f"\nKey exists in Redis: {exists}")

    # Với request_per_minute = 0, function không nên tạo key trong Redis
    # và không nên rate limit
    print("\nTest result:")
    if not exists:
        print("PASS: Key does not exist in Redis as expected")
    else:
        print("FAIL: Key exists in Redis, but it should not")


# Test case 4: Rate limiting with multiple product_ids
def test_multiple_product_ids():
    """Kiểm tra chức năng rate limiting với nhiều product_id khác nhau"""
    print(f"\n=== Test 4: Multiple Product IDs ===\n")

    # Danh sách các product_id cần kiểm tra
    product_ids = ["test_product_1", "test_product_2", "test_product_3"]
    request_per_minute = 3  # Giới hạn 3 request/phút

    # Xóa các key cũ nếu có
    for product_id in product_ids:
        lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
        redis_client.delete(lock_key)

    print(f"Testing with multiple product_ids and request_per_minute = {request_per_minute}")

    # Gọi hàm với mỗi product_id 5 lần
    results = {}
    for product_id in product_ids:
        print(f"\nTesting with product_id = {product_id}")
        rate_limited_count = 0

        for i in range(5):
            was_rate_limited = debug_rate_limit(product_id, request_per_minute)
            if was_rate_limited:
                rate_limited_count += 1
            print(f"Request {i+1}: {'Rate limited' if was_rate_limited else 'Not rate limited'}")

        results[product_id] = rate_limited_count

    print(f"\nResults:")
    for product_id, count in results.items():
        print(f"{product_id}: {count} rate limited requests")

        # Kiểm tra xem có ít nhất 2 request bị rate limit không
        assert count >= 2, f"Expected at least 2 rate limited requests for {product_id}, got {count}"


# Test case 5: Rate limiting with burst traffic
def test_burst_traffic():
    """Kiểm tra chức năng rate limiting với traffic burst"""
    print(f"\n=== Test 5: Burst Traffic ===\n")

    # Xóa key cũ nếu có
    product_id = "test_burst_traffic"
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    redis_client.delete(lock_key)

    request_per_minute = 10  # Giới hạn 10 request/phút

    print(f"Testing with request_per_minute = {request_per_minute}")
    print("Sending 20 requests in burst...")

    # Gọi hàm 20 lần liên tiếp
    start_time = time.time()
    rate_limited_count = 0

    for i in range(20):
        was_rate_limited = debug_rate_limit(product_id, request_per_minute)
        if was_rate_limited:
            rate_limited_count += 1

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\nResults:")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Rate limited requests: {rate_limited_count}")
    print(f"Average time per request: {total_time/20:.2f} seconds")

    # Kiểm tra số lượng request trong Redis
    count = redis_client.zcard(lock_key)
    print(f"Number of requests in Redis: {count}")

    # Kiểm tra xem có ít nhất 10 request bị rate limit không
    assert rate_limited_count >= 10, f"Expected at least 10 rate limited requests, got {rate_limited_count}"


# Test case 6: Rate limiting with distributed requests over time
def test_distributed_requests():
    """Kiểm tra chức năng rate limiting với các request phân tán theo thời gian"""
    print(f"\n=== Test 6: Distributed Requests ===\n")

    # Xóa key cũ nếu có
    product_id = "test_distributed_requests"
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    redis_client.delete(lock_key)

    request_per_minute = 5  # Giới hạn 5 request/phút

    print(f"Testing with request_per_minute = {request_per_minute}")
    print("Sending 10 requests with 2-second intervals...")

    # Gọi hàm 10 lần với khoảng thời gian 2 giây giữa các lần gọi
    start_time = time.time()
    rate_limited_count = 0

    for i in range(10):
        print(f"\nRequest {i+1}...")
        was_rate_limited = debug_rate_limit(product_id, request_per_minute)
        if was_rate_limited:
            rate_limited_count += 1

        # Chờ 2 giây trước khi gọi request tiếp theo
        if i < 9:  # Không chờ sau request cuối cùng
            time.sleep(2)

    end_time = time.time()
    total_time = end_time - start_time

    print(f"\nResults:")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Rate limited requests: {rate_limited_count}")
    print(f"Average time per request: {total_time/10:.2f} seconds")

    # Kiểm tra số lượng request trong Redis
    count = redis_client.zcard(lock_key)
    print(f"Number of requests in Redis: {count}")

    # Kiểm tra xem số lượng request có đúng không
    # Với khoảng thời gian 2 giây giữa các request, chúng ta sẽ có tối đa 30 request trong 1 phút
    # Vì vậy, với request_per_minute = 5, chúng ta sẽ có ít nhất 5 request bị rate limit
    assert rate_limited_count >= 5, f"Expected at least 5 rate limited requests, got {rate_limited_count}"


def test_old_entries_removal():
    # Xóa key cũ nếu có
    product_id = "test_old_entries"
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    redis_client.delete(lock_key)

    print(f"\n=== Test 2: Old Entries Removal ===\n")

    # Thêm các mục cũ (70 giây trước)
    current_time = int(time.time())
    old_time = current_time - 70  # 70 giây trước

    print(f"Adding old entries from {old_time} (70 seconds ago)...")
    for i in range(5):
        unique_timestamp = old_time + i
        redis_client.zadd(lock_key, {unique_timestamp: old_time})

    # Thêm các mục mới (10 giây trước)
    recent_time = current_time - 10  # 10 giây trước
    print(f"Adding recent entries from {recent_time} (10 seconds ago)...")
    for i in range(3):
        unique_timestamp = recent_time + i
        redis_client.zadd(lock_key, {unique_timestamp: recent_time})

    # Kiểm tra số lượng mục trước khi gọi hàm
    count_before = redis_client.zcard(lock_key)
    print(f"Number of entries before calling set_rate_limit_per_minute: {count_before}")

    # Hiển thị các timestamp trước khi gọi hàm
    entries_before = redis_client.zrange(lock_key, 0, -1, withscores=True)
    print(f"Timestamps before: {entries_before}")

    # Gọi hàm set_rate_limit_per_minute
    print("\nCalling set_rate_limit_per_minute...")
    debug_rate_limit(product_id, request_per_minute=10)

    # Kiểm tra số lượng mục sau khi gọi hàm
    count_after = redis_client.zcard(lock_key)
    print(f"Number of entries after calling set_rate_limit_per_minute: {count_after}")

    # Hiển thị các timestamp sau khi gọi hàm
    entries_after = redis_client.zrange(lock_key, 0, -1, withscores=True)
    print(f"Timestamps after: {entries_after}")

    # Kiểm tra xem các mục cũ đã bị xóa chưa
    old_entries_removed = count_before > count_after
    print(f"Old entries removed: {old_entries_removed}")
    print(f"Number of entries removed: {count_before - count_after}")


def test_key_expiration():
    # Xóa key cũ nếu có
    product_id = "test_expiration"
    lock_key = f"A12:KAFKA_FLAG:PO_CONSUMER:RATELIMIT:MINUTE:{product_id}"
    redis_client.delete(lock_key)

    print(f"\n=== Test 3: Key Expiration ===\n")

    # Gọi hàm set_rate_limit_per_minute
    print("Calling set_rate_limit_per_minute...")
    debug_rate_limit(product_id, request_per_minute=10)

    # Kiểm tra thời gian hết hạn của key
    ttl = redis_client.ttl(lock_key)
    print(f"TTL of key: {ttl} seconds")

    # Kiểm tra xem thời gian hết hạn có đúng không (gấp đôi thời gian cửa sổ)
    expected_ttl = 60 * 2  # 60 seconds * 2 = 120 seconds
    print(f"Expected TTL: {expected_ttl} seconds")

    # Kiểm tra xem thời gian hết hạn có gần đúng không (chấp nhận sai số 5 giây)
    is_ttl_correct = abs(ttl - expected_ttl) <= 5
    print(f"TTL is correct: {is_ttl_correct}")


if __name__ == "__main__":
    # Chạy tất cả các test case
    print("\n=== Running all test cases ===")

    # Chọn các test case muốn chạy
    choice = input("\nChọn test case muốn chạy (1-8, all, hoặc exit): ").strip().lower()

    while choice != "exit":
        if choice == "1" or choice == "all":
            test_basic_rate_limiting()

        if choice == "2" or choice == "all":
            test_different_rate_limits()

        if choice == "3" or choice == "all":
            test_zero_rate_limit()

        if choice == "4" or choice == "all":
            test_multiple_product_ids()

        if choice == "5" or choice == "all":
            test_burst_traffic()

        if choice == "6" or choice == "all":
            test_distributed_requests()

        if choice == "7" or choice == "all":
            test_old_entries_removal()

        if choice == "8" or choice == "all":
            test_key_expiration()

        choice = input("\nChọn test case muốn chạy (1-8, all, hoặc exit): ").strip().lower()

    print("\n=== All tests completed ===")
