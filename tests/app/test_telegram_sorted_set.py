#!/usr/bin/env python
# -*- coding: utf-8 -*-
import time
import json
import pytest
import random
from unittest.mock import patch, MagicMock

from app.extensions import redis_client
from app.services.telegram_service import TelegramService
from app.queue import QueueService

# Clear Redis keys before tests
def clear_redis_keys():
    # Clear active groups
    redis_client.delete("A12:TELEGRAM:ACTIVE_GROUPS")
    
    # Clear notices keys
    for key in redis_client.keys("A12:TELEGRAM:NOTICES:*"):
        redis_client.delete(key)
        
    # Clear sent notices keys
    for key in redis_client.keys("A12:TELEGRAM:SENT_NOTICES:*"):
        redis_client.delete(key)
        
    # Clear rate limit key
    redis_client.delete("A12:TELEGRAM:RATE_LIMIT")

# Test the sorted set implementation
def test_sorted_set_implementation():
    """Test the sorted set implementation of the Telegram service"""
    # Clear Redis keys
    clear_redis_keys()
    
    # Create a test group ID
    group_id = f"test_group_{random.randint(1000, 9999)}"
    
    # Create a TelegramService instance
    telegram_service = TelegramService()
    
    # Mock the send_telegram_message_to_group function
    with patch("app.telegram_bot.send_telegram_message_to_group") as mock_send:
        # Configure the mock to return a successful response
        mock_send.return_value = MagicMock()
        
        # Add messages with different order values
        messages = [
            {"group_id": group_id, "order": 3, "message": "Message with order 3", "message_id": f"msg_{group_id}_3"},
            {"group_id": group_id, "order": 1, "message": "Message with order 1", "message_id": f"msg_{group_id}_1"},
            {"group_id": group_id, "order": 2, "message": "Message with order 2", "message_id": f"msg_{group_id}_2"},
            {"group_id": group_id, "order": 0, "message": "Final message with order 0", "message_id": f"msg_{group_id}_0"}
        ]
        
        # Add the messages to the sorted set
        for message in messages:
            telegram_service._add_telegram_message_sorted_set(message)
        
        # Verify that the group was added to active groups
        assert redis_client.sismember("A12:TELEGRAM:ACTIVE_GROUPS", group_id)
        
        # Verify that the messages were added to the sorted set
        notices_key = f"A12:TELEGRAM:NOTICES:{group_id}"
        assert redis_client.zcard(notices_key) == 4
        
        # Process the messages
        telegram_service._process_group_messages(group_id)
        
        # Verify that the message with order=1 was sent
        mock_send.assert_called_once_with("Message with order 1")
        
        # Verify that the sorted set was deleted
        assert redis_client.zcard(notices_key) == 0
        
        # Verify that the message ID was saved to sent notices
        sent_notices_key = f"A12:TELEGRAM:SENT_NOTICES:{group_id}"
        sent_notices = redis_client.lrange(sent_notices_key, 0, -1)
        assert len(sent_notices) == 1
        assert sent_notices[0].decode('utf-8') == f"msg_{group_id}_1"
        
        # Reset the mock
        mock_send.reset_mock()
        
        # Add the final message (order=0)
        telegram_service._add_telegram_message_sorted_set(messages[3])
        
        # Process the messages again
        telegram_service._process_group_messages(group_id)
        
        # Verify that no message was sent (since order=0)
        mock_send.assert_not_called()
        
        # Verify that the group was removed from active groups
        assert not redis_client.sismember("A12:TELEGRAM:ACTIVE_GROUPS", group_id)
        
        # Verify that the sorted set was deleted
        assert redis_client.zcard(notices_key) == 0

# Test the queue service integration
def test_queue_service_integration():
    """Test the integration of the queue service with the sorted set implementation"""
    # Clear Redis keys
    clear_redis_keys()
    
    # Create a test group ID
    group_id = f"test_group_{random.randint(1000, 9999)}"
    
    # Create a QueueService instance
    queue_service = QueueService()
    
    # Mock the add_telegram_message method
    with patch("app.services.telegram_service.TelegramService.add_telegram_message") as mock_add:
        # Configure the mock to return a message ID
        mock_add.return_value = "test_message_id"
        
        # Add a message through the queue service
        message_data = {
            "group_id": group_id,
            "order": 1,
            "message": "Test message",
            "message_id": f"msg_{group_id}_1"
        }
        
        message_id = queue_service.add_task("telegram", message_data)
        
        # Verify that the add_telegram_message method was called
        mock_add.assert_called_once_with(message_data)
        
        # Verify that the message ID was returned
        assert message_id == "test_message_id"

if __name__ == "__main__":
    # Run the tests
    test_sorted_set_implementation()
    test_queue_service_integration()
    print("All tests passed!")
