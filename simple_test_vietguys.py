#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple test để kiểm tra logic lựa chọn cấu hình <PERSON>Guys
Không cần dependencies phức tạp
"""

def test_config_selection_logic():
    """Test logic lựa chọn cấu hình"""
    print("=== Test Config Selection Logic ===")
    
    def get_vietguys_config_type(product):
        """Simulate logic trong ContextCodeApi.__get_behavior_api"""
        if not product:
            return 'default'
        
        contract_signed_with = product.get('contract_signed_with')
        if contract_signed_with == 'urbox':
            return 'urbox'
        elif contract_signed_with == 'jomo':
            return 'jomo'
        else:
            return 'default'
    
    # Test cases
    test_cases = [
        ({"contract_signed_with": "urbox"}, "urbox"),
        ({"contract_signed_with": "jomo"}, "jomo"),
        ({"contract_signed_with": "other"}, "default"),
        ({"contract_signed_with": None}, "default"),
        ({}, "default"),
        (None, "default")
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, (product, expected) in enumerate(test_cases, 1):
        result = get_vietguys_config_type(product)
        status = "✅" if result == expected else "❌"
        print(f"{i}. Product: {product}")
        print(f"   Expected: {expected}, Got: {result} {status}")
        if result == expected:
            passed += 1
    
    print(f"\nConfig Selection Test: {passed}/{total} passed")
    return passed == total

def test_config_loading_logic():
    """Test logic load cấu hình"""
    print("\n=== Test Config Loading Logic ===")
    
    def load_config_simulation(config_type):
        """Simulate logic trong BehaviorVietGuysApi._load_config"""
        configs = {
            'urbox': {
                'title': 'VietGuysApi-Urbox',
                'username': 'urbox_username',
                'secret_code': 'urbox_secret'
            },
            'jomo': {
                'title': 'VietGuysApi-Jomo', 
                'username': 'jomo_username',
                'secret_code': 'jomo_secret'
            },
            'default': {
                'title': 'VietGuysApi',
                'username': 'default_username', 
                'secret_code': 'default_secret'
            }
        }
        
        return configs.get(config_type, configs['default'])
    
    # Test cases
    test_cases = ['urbox', 'jomo', 'default', 'unknown']
    expected_titles = ['VietGuysApi-Urbox', 'VietGuysApi-Jomo', 'VietGuysApi', 'VietGuysApi']
    
    passed = 0
    total = len(test_cases)
    
    for i, (config_type, expected_title) in enumerate(zip(test_cases, expected_titles), 1):
        config = load_config_simulation(config_type)
        result_title = config['title']
        status = "✅" if result_title == expected_title else "❌"
        print(f"{i}. Config type: {config_type}")
        print(f"   Expected title: {expected_title}, Got: {result_title} {status}")
        if result_title == expected_title:
            passed += 1
    
    print(f"\nConfig Loading Test: {passed}/{total} passed")
    return passed == total

def test_integration_flow():
    """Test luồng tích hợp hoàn chỉnh"""
    print("\n=== Test Integration Flow ===")
    
    def simulate_consumer_flow(payload):
        """Simulate luồng trong consumer_po_order_code.py"""
        product = payload.get("product")
        if not product:
            return None
            
        supplier = product.get("supplier", {})
        supplier_api_config = supplier.get("supplier_api_config", {})
        class_api = supplier_api_config.get("class_api")
        
        # Chỉ xử lý VietGuys
        if class_api != "vietguys_v1":
            return f"Other supplier: {class_api}"
        
        # Logic lựa chọn config cho VietGuys
        contract_signed_with = product.get('contract_signed_with')
        if contract_signed_with == 'urbox':
            config_type = 'urbox'
        elif contract_signed_with == 'jomo':
            config_type = 'jomo'
        else:
            config_type = 'default'
        
        return f"VietGuys-{config_type}"
    
    # Test cases
    test_cases = [
        {
            "payload": {
                "product": {
                    "contract_signed_with": "urbox",
                    "supplier": {
                        "supplier_api_config": {
                            "class_api": "vietguys_v1"
                        }
                    }
                }
            },
            "expected": "VietGuys-urbox"
        },
        {
            "payload": {
                "product": {
                    "contract_signed_with": "jomo",
                    "supplier": {
                        "supplier_api_config": {
                            "class_api": "vietguys_v1"
                        }
                    }
                }
            },
            "expected": "VietGuys-jomo"
        },
        {
            "payload": {
                "product": {
                    "contract_signed_with": None,
                    "supplier": {
                        "supplier_api_config": {
                            "class_api": "vietguys_v1"
                        }
                    }
                }
            },
            "expected": "VietGuys-default"
        },
        {
            "payload": {
                "product": {
                    "contract_signed_with": "urbox",
                    "supplier": {
                        "supplier_api_config": {
                            "class_api": "vmg_v1"
                        }
                    }
                }
            },
            "expected": "Other supplier: vmg_v1"
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        payload = test_case["payload"]
        expected = test_case["expected"]
        result = simulate_consumer_flow(payload)
        status = "✅" if result == expected else "❌"
        
        contract = payload["product"].get("contract_signed_with")
        supplier_type = payload["product"]["supplier"]["supplier_api_config"]["class_api"]
        
        print(f"{i}. Supplier: {supplier_type}, Contract: {contract}")
        print(f"   Expected: {expected}, Got: {result} {status}")
        
        if result == expected:
            passed += 1
    
    print(f"\nIntegration Flow Test: {passed}/{total} passed")
    return passed == total

def main():
    """Chạy tất cả tests"""
    print("🚀 Starting Simple VietGuys Config Tests...")
    
    tests = [
        test_config_selection_logic,
        test_config_loading_logic,
        test_integration_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Overall Test Results: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 All tests passed! Logic implementation is correct.")
    else:
        print("⚠️  Some tests failed. Please check the logic.")

if __name__ == "__main__":
    main()
