include:
  remote: https://git.urbox.vn/urdevops/ci-templates/raw/master/includes/default-template-prod-argocd.yaml

variables:
  URBOX_PROJECT_NAME: a12-codex
  URBOX_PROJECT_NAME_SCALE: a12-codex-worker



push-image:a12-codex-worker:
  stage: update-image
  needs:
    - job: build
  tags:
    - production
  image: 763240221275.dkr.ecr.ap-southeast-1.amazonaws.com/argocd:tool-cicd
  environment:
    name: production
  before_script:
    - git remote set-url origin https://${GITOPS_USER}:${GITOPS_TOKEN}@${GITOPS_REPO_PROD}
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Auto Deploy"
  script:
    - |
      git config pull.ff only 
      git clone https://${GITOPS_USER}:${GITOPS_TOKEN}@${GITOPS_REPO_PROD} -b master
      cd production-workload/urbox-services/production/${URBOX_PROJECT_NAME_SCALE}
      sed -i "/image:/,/tag:/ s|tag:.*|tag: release-${CI_COMMIT_TAG}|g" values.yaml
      git add .
      git commit -m "${CI_COMMIT_REF_NAME} - ${CI_PROJECT_NAME} - ${CI_COMMIT_AUTHOR} - ${CI_COMMIT_MESSAGE}" --allow-empty
      git pull 
      git push
  only:
    - /^v(\d+\.)?(\d+\.)?(\*|\d+)$/

########################################### Deploy ##########################################
deploy:a12-codex-worker:
  stage: deploy
  needs:
    - job: push-image:a12-codex-worker
  tags:
    - production
  environment:
    name: production
  image:
    name: 763240221275.dkr.ecr.ap-southeast-1.amazonaws.com/argocd:tool-cicd
  script:
    - argocd login ${ARGOCD_URL_PROD} --username ${ARGOCD_USER} --password ${ARGOCD_PASS_HCM_PRO} --skip-test-tls  --grpc-web
    - argocd app get ${ENVIRONMENT}-${URBOX_PROJECT_NAME_SCALE} --server ${ARGOCD_URL_PROD} --hard-refresh
  retry: 2
  only:
    - /^v(\d+\.)?(\d+\.)?(\*|\d+)$/
